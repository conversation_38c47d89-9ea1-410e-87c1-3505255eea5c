import asyncio
from bubus import EventBus, BaseEvent

# Create an event bus
bus = EventBus()

# Define custom events
class UserLoginEvent(BaseEvent):
    username: str
    timestamp: float

class UserLogoutEvent(BaseEvent):
    username: str
    reason: str

class SystemNotificationEvent(BaseEvent):
    message: str
    level: str

# Event handlers
async def handle_login(event: UserLoginEvent):
    print(f"🔐 User {event.username} logged in at {event.timestamp}")
    return {"status": "login_processed", "user": event.username}

async def handle_logout(event: UserLogoutEvent):
    print(f"👋 User {event.username} logged out: {event.reason}")
    return {"status": "logout_processed"}

async def handle_any_event(event: BaseEvent):
    print(f"📡 Event received: {event.event_type}")

async def handle_notifications(event: SystemNotificationEvent):
    print(f"🔔 {event.level.upper()}: {event.message}")

# Register event handlers
bus.on(UserLoginEvent, handle_login)
bus.on(UserLogoutEvent, handle_logout)
bus.on(SystemNotificationEvent, handle_notifications)
bus.on('*', handle_any_event)  # Listen to ALL events

async def main():
    print("🚀 Starting EventBus demo...\n")
    
    # Dispatch events and await results
    print("1. Dispatching login event...")
    login_result = await bus.dispatch(UserLoginEvent(username="alice", timestamp=1234567890))
    print(f"   Result: {login_result.event_result()}\n")
    
    print("2. Dispatching notification...")
    await bus.dispatch(SystemNotificationEvent(message="System maintenance in 5 minutes", level="warning"))
    print()
    
    print("3. Dispatching logout event...")
    logout_result = await bus.dispatch(UserLogoutEvent(username="alice", reason="session_timeout"))
    print(f"   Result: {logout_result.event_result()}\n")
    
    print("4. Multiple events in sequence...")
    events = [
        UserLoginEvent(username="bob", timestamp=1234567900),
        SystemNotificationEvent(message="New feature deployed", level="info"),
        UserLogoutEvent(username="bob", reason="manual_logout")
    ]
    
    for event in events:
        result = await bus.dispatch(event)
        if hasattr(result, 'event_result'):
            print(f"   {event.event_type} result: {result.event_result()}")
    
    print("\n✅ EventBus demo completed!")

if __name__ == "__main__":
    asyncio.run(main())
