---
title: "Quickstart"
description: "Learn how to get started with the Browser Use Cloud API"
icon: "cloud"
---

The Browser Use Cloud API lets you create and manage browser automation agents programmatically. Each agent can execute tasks and provide real-time feedback through a live preview URL.

## Prerequisites

<Note>
  You need an active subscription and an API key from
  [cloud.browser-use.com/billing](https://cloud.browser-use.com/billing)
</Note>

## Pricing

The Browser Use Cloud API pricing consists of two components:

1. **Task Initialization Cost**: $0.01 per started task
2. **Task Step Cost**: Additional cost based on the specific model used for each step

### LLM Model Step Pricing

The following table shows the total cost per step for each available LLM model:

| Model                            | Cost per Step |
| -------------------------------- | ------------- |
| GPT-4o                           | $0.03         |
| GPT-4o mini                      | $0.01         |
| GPT-4.1                          | $0.03         |
| GPT-4.1 mini                     | $0.01         |
| O4 mini                          | $0.02         |
| O3                               | $0.03         |
| Gemini 2.0 Flash                 | $0.01         |
| Gemini 2.0 Flash Lite            | $0.01         |
| Gemini 2.5 Flash Preview (04/17) | $0.01         |
| Gemini 2.5 Flash                 | $0.01         |
| Gemini 2.5 Pro                   | $0.03         |
| Claude 3.7 Sonnet (2025-02-19)   | $0.03         |
| Claude Sonnet 4 (2025-05-14)     | $0.03         |
| Llama 4 Maverick 17B Instruct    | $0.01         |

### Example Cost Calculation

For example, using GPT-4.1 for a 10 step task:

- Task initialization: $0.01
- 10 steps × $0.03 per step
- Total cost: $0.31

## Creating Your First Agent

Create a new browser automation task by providing instructions in natural language:

```bash
curl -X POST https://api.browser-use.com/api/v1/run-task \
  -H "Authorization: Bearer your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{
    "task": "Go to google.com and search for Browser Use"
  }'
```

The API returns a task ID that you can use to manage the task and check the live preview URL.

<Note>
  The task response includes a `live_url` that you can embed in an iframe to
  watch and control the agent in real-time.
</Note>

## Managing Tasks

Control running tasks with these operations:

<AccordionGroup>
  <Accordion title="Pause/Resume Tasks">
    Temporarily pause task execution with [`/api/v1/pause-task`](/cloud/api-v1/pause-task) and resume with
    [`/api/v1/resume-task`](/cloud/api-v1/resume-task). Useful for manual inspection or intervention.
  </Accordion>

  <Accordion title="Stop Tasks">
    Permanently stop a task using [`/api/v1/stop-task`](/cloud/api-v1/stop-task). The task cannot be
    resumed after being stopped.
  </Accordion>
</AccordionGroup>

For detailed API documentation, see the tabs on the left, which include the full coverage of the API.

<Note>
  Need help? Contact our support <NAME_EMAIL> or join our
  [Discord community](https://link.browser-use.com/discord)
</Note>
