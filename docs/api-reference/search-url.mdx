---
title: "Search URL"
api: "POST https://api.browser-use.com/api/v1/search-url"
description: "Extract specific content from a given URL using AI"
---

## Overview

Extract content from a specific website by navigating it in real-time. Clicks through pages to find exactly what you need.

💡 **Complete working example**: [search_url.py](https://github.com/browser-use/browser-use/blob/main/examples/search/search_url.py)

## Request

<ParamField body="url" type="string" required>
  The URL to extract content from
</ParamField>

<ParamField body="query" type="string" required>
  What specific content to look for and extract from the URL
</ParamField>

<ParamField body="depth" type="integer" default="2">
  How deep to navigate within the website (2-5). Higher depth = more thorough exploration through multiple page clicks.
</ParamField>

## Response

<ResponseField name="url" type="string">
  The URL that was processed
</ResponseField>

<ResponseField name="content" type="string">
  Extracted content relevant to the search query
</ResponseField>

<ResponseExample>
```json Response
{
  "url": "https://example.com",
  "content": "Relevant content extracted from the website based on your query..."
}
```
</ResponseExample>

## Example

<RequestExample>
```python Python
import aiohttp
import asyncio

async def search_website():
    payload = {
        "url": "https://www.artificialintelligence-news.com/",
        "query": "Latest AI News",
        "depth": 2
    }
    
    headers = {
        "Authorization": "Bearer YOUR_API_KEY",
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            "https://api.browser-use.com/api/v1/search-url",
            json=payload,
            headers=headers
        ) as response:
            return await response.json()

result = asyncio.run(search_website())
print(result)
```

```javascript JavaScript
const response = await fetch('https://api.browser-use.com/api/v1/search-url', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    url: 'https://www.artificialintelligence-news.com/',
    query: 'Latest AI News',
    depth: 2
  })
});

const result = await response.json();
console.log(result);
```

```bash cURL
curl -X POST "https://api.browser-use.com/api/v1/search-url" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.artificialintelligence-news.com/",
    "query": "Latest AI News",
    "depth": 2
  }'
```
</RequestExample>

## Pricing

**Cost per request**: `1 cent × depth`

Examples:
- depth=2 = 2 cents per request (default value)
- depth=3 = 3 cents per request
- depth=4 = 4 cents per request
- depth=5 = 5 cents per request 
