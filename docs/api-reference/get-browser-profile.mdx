---
title: "Get Browser Profile"
api: "GET /api/v1/browser-profiles/{profile_id}"
description: "Returns information about a specific browser profile and its configuration settings."
---

Returns information about a specific browser profile and its configuration settings.

### Path Parameters

<ParamField path="profile_id" type="string" required>
  ID of the browser profile to retrieve
</ParamField>

### Response

<ResponseField name="profile_id" type="string">
  Unique identifier for the browser profile
</ResponseField>
<ResponseField name="profile_name" type="string">
  Name of the browser profile
</ResponseField>
<ResponseField name="description" type="string">
  Description of the profile
</ResponseField>
<ResponseField name="persist" type="boolean">
  Save cookies, local storage, and session data between tasks
</ResponseField>
<ResponseField name="ad_blocker" type="boolean">
  Block ads and popups during automated tasks
</ResponseField>
<ResponseField name="proxy" type="boolean">
  Route traffic through mobile proxies for better stealth
</ResponseField>
<ResponseField name="proxy_country_code" type="string">
  Country code for the proxy
</ResponseField>
<ResponseField name="browser_viewport_width" type="integer">
  Browser viewport width in pixels
</ResponseField>
<ResponseField name="browser_viewport_height" type="integer">
  Browser viewport height in pixels
</ResponseField>

<RequestExample>
```python python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

profile_id = 'profile_1234567890abcdef'
response = requests.get(f'{BASE_URL}/browser-profiles/{profile_id}', headers=HEADERS)
profile = response.json()
print(profile)

````

```bash curl
curl --request GET \
  --url https://api.browser-use.com/api/v1/browser-profiles/profile_1234567890abcdef \
  --header 'Authorization: Bearer <token>'
````

</RequestExample>

<ResponseExample>
```json 200
{
  "profile_id": "profile_1234567890abcdef",
  "profile_name": "Default Profile",
  "description": "Main automation profile",
  "persist": true,
  "ad_blocker": true,
  "proxy": true,
  "proxy_country_code": "US",
  "browser_viewport_width": 1280,
  "browser_viewport_height": 960
}
```
</ResponseExample>
