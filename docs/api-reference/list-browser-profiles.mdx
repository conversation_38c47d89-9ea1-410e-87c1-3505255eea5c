---
title: "List Browser Profiles"
api: "GET /api/v1/browser-profiles"
description: "Returns a paginated list of all browser profiles belonging to the user."
---

Returns a paginated list of all browser profiles belonging to the user, ordered by creation date. Each profile includes configuration like ad blocker settings, proxy settings, and viewport dimensions.

<Note>
  Pay-as-you-go users can only have one profile. Subscription users can create
  multiple profiles.
</Note>

### Query Parameters

<ParamField query="page" type="integer" default="1">
  Page number (minimum: 1)
</ParamField>
<ParamField query="limit" type="integer" default="10">
  Number of items per page (minimum: 1)
</ParamField>

### Response

<ResponseField name="profiles" type="array">
  List of browser profiles
  <ResponseField name="profile_id" type="string">
    Unique identifier for the browser profile
  </ResponseField>
  <ResponseField name="profile_name" type="string">
    Name of the browser profile
  </ResponseField>
  <ResponseField name="description" type="string">
    Description of the profile
  </ResponseField>
  <ResponseField name="persist" type="boolean">
    Save cookies, local storage, and session data between tasks
  </ResponseField>
  <ResponseField name="ad_blocker" type="boolean">
    Block ads and popups during automated tasks
  </ResponseField>
  <ResponseField name="proxy" type="boolean">
    Route traffic through mobile proxies for better stealth
  </ResponseField>
  <ResponseField name="proxy_country_code" type="string">
    Country code for the proxy
  </ResponseField>
  <ResponseField name="browser_viewport_width" type="integer">
    Browser viewport width in pixels
  </ResponseField>
  <ResponseField name="browser_viewport_height" type="integer">
    Browser viewport height in pixels
  </ResponseField>
</ResponseField>
<ResponseField name="total_pages" type="integer">
  Total number of pages
</ResponseField>
<ResponseField name="page" type="integer">
  Current page number
</ResponseField>
<ResponseField name="limit" type="integer">
  Number of items per page
</ResponseField>
<ResponseField name="total_count" type="integer">
  Total number of browser profiles
</ResponseField>

<RequestExample>
```python python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

params = {'page': 1, 'limit': 10}
response = requests.get(f'{BASE_URL}/browser-profiles', headers=HEADERS, params=params)
profiles_data = response.json()
print(profiles_data)

````

```bash curl
curl --request GET \
  --url https://api.browser-use.com/api/v1/browser-profiles \
  --header 'Authorization: Bearer <token>'
````

</RequestExample>

<ResponseExample>
```json 200
{
  "profiles": [
    {
      "profile_id": "profile_1234567890abcdef",
      "profile_name": "Default Profile",
      "description": "Main automation profile",
      "persist": true,
      "ad_blocker": true,
      "proxy": true,
      "proxy_country_code": "US",
      "browser_viewport_width": 1280,
      "browser_viewport_height": 960
    }
  ],
  "total_pages": 1,
  "page": 1,
  "limit": 10,
  "total_count": 1
}
```
</ResponseExample>
